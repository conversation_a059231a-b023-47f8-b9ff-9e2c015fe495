from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
from pathlib import Path

async def update_kb_file_timestamp(kb: QdrantKnowledgeBase, file_path: str, operation: str = "update") -> None:
    """
    Update the file timestamp in knowledge base metadata.

    Args:
        kb: The knowledge base to update
        file_path: The file path that was modified
        operation: The operation type ("update", "delete")
    """
    try:
        # Only update timestamps for codebase type KBs that have timestamp tracking
        if kb.type != QdrantKnowledgebaseType.Codebase or not hasattr(kb.metadata, 'file_timestamps'):
            LOGGER.debug(f"[TIMESTAMP_UPDATE] Skipping timestamp update for KB '{kb.name}' - not a codebase type or no timestamp tracking")
            return

        normalized_path = normalize_path_format(file_path)

        if operation == "delete":
            # Remove timestamp entry for deleted files
            if normalized_path in kb.metadata.file_timestamps:
                del kb.metadata.file_timestamps[normalized_path]
                LOGGER.info(f"[TIMESTAMP_UPDATE] Removed timestamp entry for deleted file '{normalized_path}' from KB '{kb.name}'")
            else:
                LOGGER.debug(f"[TIMESTAMP_UPDATE] No timestamp entry found for deleted file '{normalized_path}' in KB '{kb.name}'")
        else:
            # Update timestamp for modified/added files
            current_timestamp = get_file_modification_time(file_path)
            if current_timestamp > 0:
                kb.metadata.update_file_timestamp(file_path, current_timestamp)
                LOGGER.info(f"[TIMESTAMP_UPDATE] Updated timestamp for file '{normalized_path}' in KB '{kb.name}': {current_timestamp}ms")
            else:
                LOGGER.warning(f"[TIMESTAMP_UPDATE] Could not get valid timestamp for file '{file_path}' in KB '{kb.name}'")

        # Persist the updated metadata to the database
        try:
            kb.save()
            LOGGER.debug(f"[TIMESTAMP_UPDATE] Persisted timestamp changes to database for KB '{kb.name}'")
        except Exception as e:
            LOGGER.error(f"[TIMESTAMP_UPDATE] Failed to persist timestamp changes for KB '{kb.name}': {e}")

    except Exception as e:
        LOGGER.error(f"[TIMESTAMP_UPDATE] Error updating timestamp for file '{file_path}' in KB '{kb.name}': {e}")


async def delete_file_chunks_from_kb(kb: QdrantKnowledgeBase, file_path: str) -> int:
    """Delete all chunks for a specific file from a knowledge base"""
    try:
        client = get_db_client()
        total_chunks_deleted = 0
        batch_size = 1000  # Process chunks in batches to manage memory

        # Create filter to match the specific file path
        file_filter = Filter(
            must=[
                FieldCondition(
                    key="metadata.file",
                    match=MatchValue(value=file_path)
                )
            ]
        )

        LOGGER.debug(f"[CHUNK_DELETE] Starting chunk retrieval for file '{file_path}' from KB '{kb.name}'")
        
        # Use pagination to handle large number of chunks
        offset = None
        while True:
            # Get batch of chunks
            scroll_result = client.scroll(
                collection_name=kb.id,
                scroll_filter=file_filter,
                limit=batch_size,
                with_payload=True,  # Need payload for detailed logging
                with_vectors=False,  # Don't need vectors for deletion
                offset=offset
            )
            
            current_batch = scroll_result[0]
            offset = scroll_result[1]  # Update offset for next iteration
            
            batch_count = len(current_batch)
            if batch_count == 0:
                break
                
            total_chunks_deleted += batch_count
            
            # Log batch information
            LOGGER.info(f"[CHUNK_DELETE] Processing batch of {batch_count} chunks from KB '{kb.name}'")
            
            # Log detailed chunk information for debugging
            for i, chunk in enumerate(current_batch):
                chunk_id = chunk.id
                chunk_payload = chunk.payload or {}
                chunk_metadata = chunk_payload.get('metadata', {})
                chunk_content = chunk_metadata.get('content', 'No content available')
                chunk_name = chunk_metadata.get('name', 'Unknown')
                
                # Truncate content for logging
                content_preview = chunk_content[:200] + "..." if len(chunk_content) > 200 else chunk_content
                LOGGER.debug(
                    f"[CHUNK_DELETE] Processing chunk {i+1}/{batch_count} "
                    f"(Total: {total_chunks_deleted}) - "
                    f"ID: {chunk_id}, Name: '{chunk_name}', "
                    f"Content: '{content_preview}'"
                )
            
            # Delete current batch
            LOGGER.debug(f"[CHUNK_DELETE] Deleting batch of {batch_count} chunks from KB '{kb.name}'")
            client.delete(
                collection_name=kb.id,
                points_selector=file_filter
            )
            
            # Break if this was the last batch
            if len(current_batch) < batch_size:
                break
                
            # Memory optimization: Clear batch data
            del current_batch
            
        if total_chunks_deleted > 0:
            LOGGER.info(f"[CHUNK_DELETE] Successfully deleted {total_chunks_deleted} total chunks for file '{file_path}' from KB '{kb.name}'")
        else:
            LOGGER.debug(f"[CHUNK_DELETE] No chunks found for file '{file_path}' in KB '{kb.name}'")
            
        return total_chunks_deleted

    except Exception as e:
        LOGGER.error(f"[CHUNK_DELETE] Error deleting chunks from KB '{kb.name}': {e}")
        LOGGER.debug(f"[CHUNK_DELETE] Full traceback: {traceback.format_exc()}")
        raise

async def process_file_chunks(file_path: str, kb: QdrantKnowledgeBase) -> List[QdrantKnowledgeBaseChunk]:
    """Process file into chunks using appropriate chunker"""
    try:
        LOGGER.debug(f"[CHUNK_PROCESS] Starting file processing for '{file_path}' in KB '{kb.name}' (type: {kb.type})")

        # For codebase type, we can use the utility function
        if kb.type == QdrantKnowledgebaseType.Codebase:
            LOGGER.debug(f"[CHUNK_PROCESS] Using codebase chunker for file '{file_path}'")
            chunks = make_qdrant_knowledgebase_chunks_from_file(file_path)

            # Update chunk metadata with correct file path and names
            for i, chunk in enumerate(chunks):
                chunk.metadata.file = file_path
                chunk.metadata.name = f"{Path(file_path).name} - chunk"
                content_length = len(chunk.metadata.content) if chunk.metadata.content else 0
                LOGGER.debug(f"[CHUNK_PROCESS] Processed chunk {i+1}: ID will be generated, Content length: {content_length} chars")

            LOGGER.info(f"[CHUNK_PROCESS] Successfully processed {len(chunks)} chunks from file '{file_path}' using codebase chunker")
            return chunks
        else:
            # For other types, use specific chunkers
            LOGGER.debug(f"[CHUNK_PROCESS] Using specific chunker for KB type '{kb.type}'")
            chunker = determine_chunker_type(kb.type, kb.metadata)
            chunks = await chunker.process()
            LOGGER.info(f"[CHUNK_PROCESS] Successfully processed {len(chunks)} chunks from file '{file_path}' using {kb.type} chunker")
            return chunks

    except Exception as e:
        LOGGER.error(f"[CHUNK_PROCESS] Error processing file '{file_path}' for KB '{kb.name}': {e}")
        raise


async def generate_embeddings_for_chunks(chunks: List[QdrantKnowledgeBaseChunk]) -> None:
    """Generate embeddings for all chunks"""
    try:
        LOGGER.debug(f"[EMBEDDING_GEN] Starting embedding generation for {len(chunks)} chunks")
        embedding_backend = EmbeddingInferenceBuilder.create()

        embeddings_generated = 0
        for i, chunk in enumerate(chunks):
            if not chunk.embeddings:  # Only generate if not already present
                LOGGER.debug(f"[EMBEDDING_GEN] Generating embedding for chunk {i+1}/{len(chunks)} - ID: {chunk.metadata.id}")
                await chunk.fill_embeddings(embedding_backend)
                embeddings_generated += 1
            else:
                LOGGER.debug(f"[EMBEDDING_GEN] Chunk {i+1}/{len(chunks)} already has embeddings - ID: {chunk.metadata.id}")

        LOGGER.info(f"[EMBEDDING_GEN] Generated embeddings for {embeddings_generated}/{len(chunks)} chunks")

    except Exception as e:
        LOGGER.error(f"[EMBEDDING_GEN] Error generating embeddings: {e}")
        raise


async def insert_chunks_to_kb(kb: QdrantKnowledgeBase, chunks: List[QdrantKnowledgeBaseChunk]) -> None:
    """Insert new chunks into knowledge base"""
    try:
        if not chunks:
            LOGGER.debug(f"[CHUNK_INSERT] No chunks to insert into KB '{kb.name}'")
            return

        LOGGER.info(f"[CHUNK_INSERT] Starting insertion of {len(chunks)} chunks into KB '{kb.name}'")
        client = get_db_client()

        # Prepare points for insertion and log chunk details
        points = []

        for i, chunk in enumerate(chunks):
            chunk_id = chunk.metadata.id
            chunk_content = chunk.metadata.content or ""
            content_preview = chunk_content[:200] + "..." if len(chunk_content) > 200 else chunk_content

            LOGGER.debug(f"[CHUNK_INSERT] Preparing chunk {i+1}/{len(chunks)} - ID: {chunk_id}, Name: '{chunk.metadata.name}', Content: '{content_preview}'")

            points.append(
                PointStruct(
                    id=chunk_id,
                    vector={"vectors": chunk.embeddings},
                    payload={"metadata": chunk.metadata.model_dump()}
                )
            )

        # Insert points in batches to avoid memory issues
        batch_size = 100
        total_batches = (len(points) + batch_size - 1) // batch_size
        LOGGER.debug(f"[CHUNK_INSERT] Inserting {len(points)} points in {total_batches} batch(es) of max {batch_size} points each")

        for batch_num, i in enumerate(range(0, len(points), batch_size), 1):
            batch = points[i:i + batch_size]
            LOGGER.debug(f"[CHUNK_INSERT] Inserting batch {batch_num}/{total_batches} with {len(batch)} points")
            client.upsert(
                collection_name=kb.id,
                points=batch
            )

        LOGGER.info(f"[CHUNK_INSERT] Successfully inserted {len(chunks)} chunks into KB '{kb.name}'")

        # Log the IDs of all newly created chunks
        chunk_ids = [chunk.metadata.id for chunk in chunks]
        LOGGER.debug(f"[CHUNK_INSERT] New chunk IDs created: {chunk_ids}")

    except Exception as e:
        LOGGER.error(f"[CHUNK_INSERT] Error inserting chunks into KB '{kb.name}': {e}")
        raise


async def update_single_kb(kb: QdrantKnowledgeBase, file_path: str) -> KBUpdateResult:
    """Update a single knowledge base with the modified file"""
    kb_start_time = time.time()
    chunks_deleted = 0
    chunks_created = 0
    error = None
    status = "failed"

    try:
        LOGGER.info(f"[KB_UPDATE] Starting update of KB '{kb.name}' (ID: {kb.id}) for file '{file_path}'")

        # Step 1: Delete existing chunks
        LOGGER.debug(f"[KB_UPDATE] Step 1/4: Deleting existing chunks for file '{file_path}' from KB '{kb.name}'")
        chunks_deleted = await delete_file_chunks_from_kb(kb, file_path)

        # Step 2: Process file into new chunks
        LOGGER.debug(f"[KB_UPDATE] Step 2/4: Processing file '{file_path}' into chunks for KB '{kb.name}'")
        new_chunks = await process_file_chunks(file_path, kb)

        # Step 3: Generate embeddings
        LOGGER.debug(f"[KB_UPDATE] Step 3/4: Generating embeddings for {len(new_chunks)} chunks in KB '{kb.name}'")
        await generate_embeddings_for_chunks(new_chunks)

        # Step 4: Insert new chunks
        LOGGER.debug(f"[KB_UPDATE] Step 4/5: Inserting {len(new_chunks)} new chunks into KB '{kb.name}'")
        await insert_chunks_to_kb(kb, new_chunks)

        # Step 5: Update file timestamp in metadata
        LOGGER.debug(f"[KB_UPDATE] Step 5/5: Updating file timestamp in metadata for KB '{kb.name}'")
        await update_kb_file_timestamp(kb, file_path, "update")

        chunks_created = len(new_chunks)
        status = "success"

        LOGGER.info(f"[KB_UPDATE] Successfully updated KB '{kb.name}': deleted {chunks_deleted}, created {chunks_created} chunks in {time.time() - kb_start_time:.3f}s")

    except Exception as e:
        error = str(e)
        status = "failed"
        LOGGER.error(f"[KB_UPDATE] Failed to update KB '{kb.name}': {e}")
        LOGGER.debug(f"[KB_UPDATE] Full traceback: {traceback.format_exc()}")

    processing_time = time.time() - kb_start_time

    return KBUpdateResult(
        kb_id=kb.id,
        kb_name=kb.name,
        status=status,
        chunks_deleted=chunks_deleted,
        chunks_created=chunks_created,
        processing_time_seconds=processing_time,
        error=error
    )


async def delete_single_kb_file(kb: QdrantKnowledgeBase, file_path: str) -> KBDeleteResult:
    """Delete all chunks for a specific file from a single knowledge base"""
    kb_start_time = time.time()
    chunks_deleted = 0
    error = None
    status = "failed"

    try:
        LOGGER.info(f"[KB_DELETE] Starting deletion of file '{file_path}' from KB '{kb.name}' (ID: {kb.id})")

        # Step 1: Delete existing chunks for the file
        LOGGER.debug(f"[KB_DELETE] Step 1/2: Deleting all chunks for file '{file_path}' from KB '{kb.name}'")
        chunks_deleted = await delete_file_chunks_from_kb(kb, file_path)

        # Step 2: Remove file timestamp from metadata
        LOGGER.debug(f"[KB_DELETE] Step 2/2: Removing file timestamp from metadata for KB '{kb.name}'")
        await update_kb_file_timestamp(kb, file_path, "delete")

        status = "success"
        LOGGER.info(f"[KB_DELETE] Successfully deleted {chunks_deleted} chunks for file '{file_path}' from KB '{kb.name}' in {time.time() - kb_start_time:.3f}s")

    except Exception as e:
        error = str(e)
        status = "failed"
        LOGGER.error(f"[KB_DELETE] Failed to delete file '{file_path}' from KB '{kb.name}': {e}")
        LOGGER.debug(f"[KB_DELETE] Full traceback: {traceback.format_exc()}")

    processing_time = time.time() - kb_start_time

    return KBDeleteResult(
        kb_id=kb.id,
        kb_name=kb.name,
        status=status,
        chunks_deleted=chunks_deleted,
        processing_time_seconds=processing_time,
        error=error
    )


# -----------------------------------------------------------------------------
# Main Route Handler
# -----------------------------------------------------------------------------

async def watchdog_file_update(request:) -> dict:
    """Handle file update notification from watchdog server"""
    start_time = time.time()
    timestamp = datetime.now(timezone.utc).isoformat()

    LOGGER.info(f"[ENDPOINT] ========== FILE UPDATE ENDPOINT CALLED ==========")
    LOGGER.info(f"[ENDPOINT] Processing file update request for: {request.file_path}")
    LOGGER.info(f"[ENDPOINT] Request timestamp: {timestamp}")

    try:
        # Step 1: Validate file path (preserves original format)
        LOGGER.debug(f"[ENDPOINT] Step 1: Validating file path '{request.file_path}'")
        file_path_str = validate_file_path(request.file_path)
        LOGGER.debug(f"[ENDPOINT] File path validation successful: '{file_path_str}'")

        # Step 2: Discovery phase - find all KBs containing this file
        LOGGER.info(f"[ENDPOINT] Step 2: Discovering knowledge bases containing file: {file_path_str}")
        LOGGER.debug(f"[ENDPOINT] Original file path: '{request.file_path}' -> Validated path: '{file_path_str}'")
        search_result = QdrantKnowledgeBase.find_file_across_all_kbs(file_path_str)
        LOGGER.info(f"[ENDPOINT] Discovery complete: Found file in {search_result.kbs_with_matches} knowledge base(s)")
        
        if not search_result.has_matches:
            LOGGER.warning(f"[ENDPOINT] File '{file_path_str}' not found in any knowledge base")
            # Debug: Log some sample file paths from KBs to help troubleshoot
            LOGGER.debug(f"[ENDPOINT] Searched {search_result.total_kbs_searched} knowledge bases")
            for kb_result in search_result.kb_results[:2]:  # Log first 2 KBs for debugging
                LOGGER.debug(f"[ENDPOINT] KB '{kb_result.kb_name}': searched for '{file_path_str}', found {kb_result.total_chunks} chunks")
            return WatchdogFileUpdateResponse(
                status="success",
                file_path=file_path_str,
                timestamp=timestamp,
                processing_time_seconds=time.time() - start_time,
                summary={
                    "total_kbs_found": 0,
                    "kbs_updated_successfully": 0,
                    "kbs_failed": 0,
                    "total_chunks_deleted": 0,
                    "total_chunks_created": 0,
                    "total_embeddings_generated": 0
                },
                kb_results=[],
                warnings=["File not found in any knowledge base"],
                next_actions=["Verify file path and knowledge base content"]
            )
        
        LOGGER.info(f"[ENDPOINT] Found file in {search_result.kbs_with_matches} knowledge base(s)")

        # Step 3: Update each knowledge base
        LOGGER.info(f"[ENDPOINT] Step 3: Starting knowledge base updates")
        kb_results = []
        successful_updates = 0
        failed_updates = 0
        total_chunks_deleted = 0
        total_chunks_created = 0

        # Get KBs that actually contain the file
        kbs_to_update = []
        LOGGER.debug(f"[ENDPOINT] Retrieving KB objects for {len([r for r in search_result.kb_results if r.has_chunks])} KBs with matching chunks")

        for kb_result in search_result.kb_results:
            if kb_result.has_chunks:
                try:
                    LOGGER.debug(f"[ENDPOINT] Retrieving KB object for '{kb_result.kb_name}' (ID: {kb_result.kb_id})")
                    kb = QdrantKnowledgeBase.get(kb_result.kb_id)
                    kbs_to_update.append(kb)
                    LOGGER.debug(f"[ENDPOINT] Successfully retrieved KB '{kb.name}'")
                except Exception as e:
                    LOGGER.error(f"[ENDPOINT] Could not retrieve KB {kb_result.kb_id}: {e}")
                    kb_results.append(KBUpdateResult(
                        kb_id=kb_result.kb_id,
                        kb_name=kb_result.kb_name,
                        status="failed",
                        chunks_deleted=0,
                        chunks_created=0,
                        processing_time_seconds=0.0,
                        error=f"Could not retrieve KB: {e}"
                    ))
                    failed_updates += 1

        # Process each KB
        LOGGER.info(f"[ENDPOINT] Processing {len(kbs_to_update)} knowledge base(s) for updates")
        for i, kb in enumerate(kbs_to_update, 1):
            LOGGER.info(f"[ENDPOINT] Processing KB {i}/{len(kbs_to_update)}: '{kb.name}' (ID: {kb.id})")
            result = await update_single_kb(kb, file_path_str)
            kb_results.append(result)

            if result.status == "success":
                successful_updates += 1
                total_chunks_deleted += result.chunks_deleted
                total_chunks_created += result.chunks_created
                LOGGER.info(f"[ENDPOINT] KB {i}/{len(kbs_to_update)} update successful: '{kb.name}'")
            else:
                failed_updates += 1
                LOGGER.error(f"[ENDPOINT] KB {i}/{len(kbs_to_update)} update failed: '{kb.name}' - {result.error}")
        
        # Step 4: Generate response
        total_processing_time = time.time() - start_time
        
        # Determine overall status
        if failed_updates == 0:
            overall_status = "success"
        elif successful_updates > 0:
            overall_status = "partial"
        else:
            overall_status = "failed"
        
        # Generate warnings and next actions
        warnings = []
        next_actions = []
        
        if failed_updates > 0:
            warnings.append(f"{failed_updates} knowledge base(s) failed to update")
            next_actions.append("Review failed KB updates in the logs")
        
        if total_chunks_created > total_chunks_deleted * 1.5:
            warnings.append("File size increased significantly - consider reviewing chunking parameters")
        
        if overall_status == "partial":
            next_actions.append("Consider manual verification of updated content")
        
        # Log operation statistics
        log_operation_stats(
            "watchdog_file_update", 
            start_time, 
            successful_updates, 
            success=(overall_status != "failed")
        )
        
        response = WatchdogFileUpdateResponse(
            status=overall_status,
            file_path=file_path_str,
            timestamp=timestamp,
            processing_time_seconds=total_processing_time,
            summary={
                "total_kbs_found": len(kbs_to_update) + failed_updates,
                "kbs_updated_successfully": successful_updates,
                "kbs_failed": failed_updates,
                "total_chunks_deleted": total_chunks_deleted,
                "total_chunks_created": total_chunks_created,
                "total_embeddings_generated": total_chunks_created
            },
            kb_results=kb_results,
            warnings=warnings,
            next_actions=next_actions
        )
        
        LOGGER.info(
            f"[ENDPOINT] ========== FILE UPDATE COMPLETED ==========\n"
            f"[ENDPOINT] Status: {overall_status}\n"
            f"[ENDPOINT] File: {file_path_str}\n"
            f"[ENDPOINT] KBs Updated: {successful_updates}/{len(kbs_to_update) + failed_updates}\n"
            f"[ENDPOINT] Total Chunks Deleted: {total_chunks_deleted}\n"
            f"[ENDPOINT] Total Chunks Created: {total_chunks_created}\n"
            f"[ENDPOINT] Processing Time: {total_processing_time:.3f}s\n"
            f"[ENDPOINT] ================================================"
        )
        
        return response
        
    except FileNotFoundError as e:
        LOGGER.error(f"File not found: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    
    except ValueError as e:
        LOGGER.error(f"Invalid request: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        total_processing_time = time.time() - start_time
        LOGGER.error(f"Unexpected error in file update: {e}")
        LOGGER.debug(f"Full traceback: {traceback.format_exc()}")
        
        # Log failed operation
        log_operation_stats(
            "watchdog_file_update", 
            start_time, 
            0, 
            success=False
        )
        
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during file update: {str(e)}"
        )


async def watchdog_file_delete(request:) -> dic:
    """Handle file deletion notification from watchdog server"""
    start_time = time.time()
    timestamp = datetime.now(timezone.utc).isoformat()

    LOGGER.info(f"[ENDPOINT] ========== FILE DELETE ENDPOINT CALLED ==========")
    LOGGER.info(f"[ENDPOINT] Processing file deletion request for: {request.file_path}")
    LOGGER.info(f"[ENDPOINT] Request timestamp: {timestamp}")

    try:
        # Step 1: Validate file path (but don't require file to exist since it's deleted)
        LOGGER.debug(f"[ENDPOINT] Step 1: Validating file path '{request.file_path}'")
        # For deletion, we don't validate file existence since the file is already deleted
        # Just do basic security checks
        if ".." in request.file_path:
            raise ValueError("Invalid file path: directory traversal detected")

        file_path_str = request.file_path
        LOGGER.debug(f"[ENDPOINT] File path validation successful: '{file_path_str}'")

        # Step 2: Discovery phase - find all KBs containing this file
        LOGGER.info(f"[ENDPOINT] Step 2: Discovering knowledge bases containing file: {file_path_str}")
        search_result = QdrantKnowledgeBase.find_file_across_all_kbs(file_path_str)
        LOGGER.info(f"[ENDPOINT] Discovery complete: Found file in {search_result.kbs_with_matches} knowledge base(s)")

        if not search_result.has_matches:
            LOGGER.warning(f"[ENDPOINT] File '{file_path_str}' not found in any knowledge base")
            # Debug: Log some sample file paths from KBs to help troubleshoot
            LOGGER.debug(f"[ENDPOINT] Searched {search_result.total_kbs_searched} knowledge bases")
            for kb_result in search_result.kb_results[:2]:  # Log first 2 KBs for debugging
                LOGGER.debug(f"[ENDPOINT] KB '{kb_result.kb_name}': searched for '{file_path_str}', found {kb_result.total_chunks} chunks")
            return WatchdogFileDeleteResponse(
                status="success",
                file_path=file_path_str,
                timestamp=timestamp,
                processing_time_seconds=time.time() - start_time,
                summary={
                    "total_kbs_found": 0,
                    "kbs_processed_successfully": 0,
                    "kbs_failed": 0,
                    "total_chunks_deleted": 0
                },
                kb_results=[],
                warnings=["File not found in any knowledge base"],
                next_actions=["Verify file path and knowledge base content"]
            )

        LOGGER.info(f"[ENDPOINT] Found file in {search_result.kbs_with_matches} knowledge base(s)")

        # Step 3: Delete from each knowledge base
        LOGGER.info(f"[ENDPOINT] Step 3: Starting knowledge base deletions")
        kb_results = []
        successful_deletions = 0
        failed_deletions = 0
        total_chunks_deleted = 0

        # Get KBs that actually contain the file
        kbs_to_process = []
        LOGGER.debug(f"[ENDPOINT] Retrieving KB objects for {len([r for r in search_result.kb_results if r.has_chunks])} KBs with matching chunks")

        for kb_result in search_result.kb_results:
            if kb_result.has_chunks:
                try:
                    LOGGER.debug(f"[ENDPOINT] Retrieving KB object for '{kb_result.kb_name}' (ID: {kb_result.kb_id})")
                    kb = QdrantKnowledgeBase.get(kb_result.kb_id)
                    kbs_to_process.append(kb)
                    LOGGER.debug(f"[ENDPOINT] Successfully retrieved KB '{kb.name}'")
                except Exception as e:
                    LOGGER.error(f"[ENDPOINT] Could not retrieve KB {kb_result.kb_id}: {e}")
                    kb_results.append(KBDeleteResult(
                        kb_id=kb_result.kb_id,
                        kb_name=kb_result.kb_name,
                        status="failed",
                        chunks_deleted=0,
                        processing_time_seconds=0.0,
                        error=f"Could not retrieve KB: {e}"
                    ))
                    failed_deletions += 1

        # Process each KB
        LOGGER.info(f"[ENDPOINT] Processing {len(kbs_to_process)} knowledge base(s) for deletions")
        for i, kb in enumerate(kbs_to_process, 1):
            LOGGER.info(f"[ENDPOINT] Processing KB {i}/{len(kbs_to_process)}: '{kb.name}' (ID: {kb.id})")
            result = await delete_single_kb_file(kb, file_path_str)
            kb_results.append(result)

            if result.status == "success":
                successful_deletions += 1
                total_chunks_deleted += result.chunks_deleted
                LOGGER.info(f"[ENDPOINT] KB {i}/{len(kbs_to_process)} deletion successful: '{kb.name}'")
            else:
                failed_deletions += 1
                LOGGER.error(f"[ENDPOINT] KB {i}/{len(kbs_to_process)} deletion failed: '{kb.name}' - {result.error}")

        # Step 4: Generate response
        total_processing_time = time.time() - start_time

        # Determine overall status
        if failed_deletions == 0:
            overall_status = "success"
        elif successful_deletions > 0:
            overall_status = "partial"
        else:
            overall_status = "failed"

        # Generate warnings and next actions
        warnings = []
        next_actions = []

        if failed_deletions > 0:
            warnings.append(f"{failed_deletions} knowledge base(s) failed to process deletion")
            next_actions.append("Review failed KB deletions in the logs")

        if total_chunks_deleted == 0 and search_result.has_matches:
            warnings.append("No chunks were deleted despite file being found in knowledge bases")
            next_actions.append("Verify chunk deletion logic and file path matching")

        if overall_status == "partial":
            next_actions.append("Consider manual verification of remaining content")

        # Log operation statistics
        log_operation_stats(
            "watchdog_file_delete",
            start_time,
            successful_deletions,
            success=(overall_status != "failed")
        )

        response = WatchdogFileDeleteResponse(
            status=overall_status,
            file_path=file_path_str,
            timestamp=timestamp,
            processing_time_seconds=total_processing_time,
            summary={
                "total_kbs_found": len(kbs_to_process) + failed_deletions,
                "kbs_processed_successfully": successful_deletions,
                "kbs_failed": failed_deletions,
                "total_chunks_deleted": total_chunks_deleted
            },
            kb_results=kb_results,
            warnings=warnings,
            next_actions=next_actions
        )

        LOGGER.info(
            f"[ENDPOINT] ========== FILE DELETE COMPLETED ==========\n"
            f"[ENDPOINT] Status: {overall_status}\n"
            f"[ENDPOINT] File: {file_path_str}\n"
            f"[ENDPOINT] KBs Processed: {successful_deletions}/{len(kbs_to_process) + failed_deletions}\n"
            f"[ENDPOINT] Total Chunks Deleted: {total_chunks_deleted}\n"
            f"[ENDPOINT] Processing Time: {total_processing_time:.3f}s\n"
            f"[ENDPOINT] ================================================"
        )

        return response

    except ValueError as e:
        LOGGER.error(f"Invalid request: {e}")
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        total_processing_time = time.time() - start_time
        LOGGER.error(f"Unexpected error in file deletion: {e}")
        LOGGER.debug(f"Full traceback: {traceback.format_exc()}")

        # Log failed operation
        log_operation_stats(
            "watchdog_file_delete",
            start_time,
            0,
            success=False
        )

        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during file deletion: {str(e)}"
        )
