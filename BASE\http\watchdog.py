import os
import time
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any
from uuid import uuid4

from logger.log import logger
from BASE.utils.path_selector import validate_file_path
from BASE.vdb.qdrant import get_qdrant_client
from BASE.services.knowledge_bases import (
    get_knowledge_base_by_id,
    list_all_knowledge_bases,
    update_knowledge_base
)
from BASE.chunkers.utils import make_chunks_from_file
from BASE.embeddings.embeddings import generate_embeddings_cloud
from qdrant_client.models import Filter, FieldCondition, MatchValue, PointStruct


# Utility functions



@logger.catch()
def normalize_path_format(file_path: str) -> str:
    """Normalize file path format for consistent comparison."""
    return str(Path(file_path).as_posix())




# Core functions for codebase knowledge bases only
@logger.catch()
async def update_kb_file_timestamp(kb_data: dict, file_path: str, operation: str = "update") -> None:
    """
    Update the file timestamp in knowledge base metadata.

    Args:
        kb_data: The knowledge base dictionary
        file_path: The file path that was modified
        operation: The operation type ("update", "delete")
    """
    try:
        # Only update timestamps for codebase type KBs that have timestamp tracking
        kb_type = kb_data.get("metadata", {}).get("type", "")
        if kb_type != "Codebase":
            logger.debug(f"[TIMESTAMP_UPDATE] Skipping timestamp update for KB '{kb_data.get('name')}' - not a codebase type")
            return

        kb_metadata = kb_data.get("metadata", {}).get("metadata", {})
        file_timestamps = kb_metadata.get("file_timestamps", {})
        normalized_path = normalize_path_format(file_path)

        if operation == "delete":
            # Remove timestamp entry for deleted files
            if normalized_path in file_timestamps:
                del file_timestamps[normalized_path]
                logger.info(f"[TIMESTAMP_UPDATE] Removed timestamp entry for deleted file '{normalized_path}' from KB '{kb_data.get('name')}'")
            else:
                logger.debug(f"[TIMESTAMP_UPDATE] No timestamp entry found for deleted file '{normalized_path}' in KB '{kb_data.get('name')}'")
        else:
            # Update timestamp for modified/added files
            current_timestamp = get_file_modification_time(file_path)
            if current_timestamp > 0:
                file_timestamps[normalized_path] = current_timestamp
                logger.info(f"[TIMESTAMP_UPDATE] Updated timestamp for file '{normalized_path}' in KB '{kb_data.get('name')}': {current_timestamp}ms")
            else:
                logger.warning(f"[TIMESTAMP_UPDATE] Could not get valid timestamp for file '{file_path}' in KB '{kb_data.get('name')}'")

        # Update the metadata structure
        kb_data["metadata"]["metadata"]["file_timestamps"] = file_timestamps

        # Persist the updated metadata to the database
        try:
            update_knowledge_base(kb_data["id"], metadata=kb_data["metadata"])
            logger.debug(f"[TIMESTAMP_UPDATE] Persisted timestamp changes to database for KB '{kb_data.get('name')}'")
        except Exception as e:
            logger.error(f"[TIMESTAMP_UPDATE] Failed to persist timestamp changes for KB '{kb_data.get('name')}': {e}")

    except Exception as e:
        logger.error(f"[TIMESTAMP_UPDATE] Error updating timestamp for file '{file_path}' in KB '{kb_data.get('name')}': {e}")


@logger.catch()
async def delete_file_chunks_from_kb(kb_data: dict, file_path: str) -> int:
    """Delete all chunks for a specific file from a knowledge base"""
    try:
        client = get_qdrant_client()
        total_chunks_deleted = 0
        batch_size = 1000  # Process chunks in batches to manage memory
        kb_id = kb_data["id"]
        kb_name = kb_data.get("name", "Unknown")

        # Create filter to match the specific file path
        file_filter = Filter(
            must=[
                FieldCondition(
                    key="file",
                    match=MatchValue(value=file_path)
                )
            ]
        )

        logger.debug(f"[CHUNK_DELETE] Starting chunk retrieval for file '{file_path}' from KB '{kb_name}'")

        # Use pagination to handle large number of chunks
        offset = None
        while True:
            # Get batch of chunks
            scroll_result = await client.scroll(
                collection_name=kb_id,
                scroll_filter=file_filter,
                limit=batch_size,
                with_payload=True,  # Need payload for detailed logging
                with_vectors=False,  # Don't need vectors for deletion
                offset=offset
            )

            current_batch = scroll_result[0]
            offset = scroll_result[1]  # Update offset for next iteration

            batch_count = len(current_batch)
            if batch_count == 0:
                break

            total_chunks_deleted += batch_count

            # Log batch information
            logger.info(f"[CHUNK_DELETE] Processing batch of {batch_count} chunks from KB '{kb_name}'")

            # Log detailed chunk information for debugging
            for i, chunk in enumerate(current_batch):
                chunk_id = chunk.id
                chunk_payload = chunk.payload or {}
                chunk_content = chunk_payload.get('content', 'No content available')
                chunk_name = chunk_payload.get('name', 'Unknown')

                # Truncate content for logging
                content_preview = chunk_content[:200] + "..." if len(chunk_content) > 200 else chunk_content
                logger.debug(
                    f"[CHUNK_DELETE] Processing chunk {i+1}/{batch_count} "
                    f"(Total: {total_chunks_deleted}) - "
                    f"ID: {chunk_id}, Name: '{chunk_name}', "
                    f"Content: '{content_preview}'"
                )

            # Delete current batch
            logger.debug(f"[CHUNK_DELETE] Deleting batch of {batch_count} chunks from KB '{kb_name}'")
            await client.delete(
                collection_name=kb_id,
                points_selector=file_filter
            )

            # Break if this was the last batch
            if len(current_batch) < batch_size:
                break

            # Memory optimization: Clear batch data
            del current_batch

        if total_chunks_deleted > 0:
            logger.info(f"[CHUNK_DELETE] Successfully deleted {total_chunks_deleted} total chunks for file '{file_path}' from KB '{kb_name}'")
        else:
            logger.debug(f"[CHUNK_DELETE] No chunks found for file '{file_path}' in KB '{kb_name}'")

        return total_chunks_deleted

    except Exception as e:
        logger.error(f"[CHUNK_DELETE] Error deleting chunks from KB '{kb_data.get('name', 'Unknown')}': {e}")
        logger.debug(f"[CHUNK_DELETE] Full traceback: {traceback.format_exc()}")
        raise

@logger.catch()
async def process_file_chunks(file_path: str, kb_data: dict) -> List[dict]:
    """Process file into chunks using functional chunker for codebase type only"""
    try:
        kb_name = kb_data.get("name", "Unknown")
        kb_type = kb_data.get("metadata", {}).get("type", "")

        logger.debug(f"[CHUNK_PROCESS] Starting file processing for '{file_path}' in KB '{kb_name}' (type: {kb_type})")

        # Only handle codebase type as per requirements
        if kb_type != "Codebase":
            raise ValueError(f"Unsupported KB type '{kb_type}'. Only Codebase type is supported.")

        logger.debug(f"[CHUNK_PROCESS] Using functional chunker for file '{file_path}'")
        chunks = make_chunks_from_file(file_path)

        # Update chunk metadata with correct file path and names
        for i, chunk in enumerate(chunks):
            chunk["metadata"]["file"] = file_path
            chunk["metadata"]["name"] = f"{Path(file_path).name} - chunk {i+1}"
            content_length = len(chunk["metadata"].get("content", ""))
            logger.debug(f"[CHUNK_PROCESS] Processed chunk {i+1}: Content length: {content_length} chars")

        logger.info(f"[CHUNK_PROCESS] Successfully processed {len(chunks)} chunks from file '{file_path}' using functional chunker")
        return chunks

    except Exception as e:
        logger.error(f"[CHUNK_PROCESS] Error processing file '{file_path}' for KB '{kb_data.get('name', 'Unknown')}': {e}")
        raise

@logger.catch()
async def generate_embeddings_for_chunks(chunks: List[dict]) -> None:
    """Generate embeddings for all chunks using cloud embeddings"""
    try:
        logger.debug(f"[EMBEDDING_GEN] Starting embedding generation for {len(chunks)} chunks using cloud embeddings")

        # Prepare content for batch embedding generation
        contents = []
        chunks_needing_embeddings = []

        for i, chunk in enumerate(chunks):
            if not chunk.get("embeddings"):  # Only generate if not already present
                content = chunk["metadata"].get("content", "")
                if content:
                    contents.append(content)
                    chunks_needing_embeddings.append(i)
                    logger.debug(f"[EMBEDDING_GEN] Chunk {i+1}/{len(chunks)} needs embedding - ID: {chunk['metadata'].get('id')}")
                else:
                    logger.warning(f"[EMBEDDING_GEN] Chunk {i+1}/{len(chunks)} has no content - ID: {chunk['metadata'].get('id')}")
            else:
                logger.debug(f"[EMBEDDING_GEN] Chunk {i+1}/{len(chunks)} already has embeddings - ID: {chunk['metadata'].get('id')}")

        if contents:
            logger.info(f"[EMBEDDING_GEN] Generating embeddings for {len(contents)} chunks using cloud service")
            embeddings = await generate_embeddings_cloud(batch=True, texts=contents)

            # Assign embeddings back to chunks
            for i, embedding in enumerate(embeddings):
                chunk_index = chunks_needing_embeddings[i]
                chunks[chunk_index]["embeddings"] = embedding
                logger.debug(f"[EMBEDDING_GEN] Assigned embedding to chunk {chunk_index+1}")

        logger.info(f"[EMBEDDING_GEN] Generated embeddings for {len(contents)}/{len(chunks)} chunks")

    except Exception as e:
        logger.error(f"[EMBEDDING_GEN] Error generating embeddings: {e}")
        raise


@logger.catch()
async def insert_chunks_to_kb(kb_data: dict, chunks: List[dict]) -> None:
    """Insert new chunks into knowledge base"""
    try:
        if not chunks:
            logger.debug(f"[CHUNK_INSERT] No chunks to insert into KB '{kb_data.get('name', 'Unknown')}'")
            return

        kb_id = kb_data["id"]
        kb_name = kb_data.get("name", "Unknown")
        logger.info(f"[CHUNK_INSERT] Starting insertion of {len(chunks)} chunks into KB '{kb_name}'")
        client = get_qdrant_client()

        # Prepare points for insertion and log chunk details
        points = []

        for i, chunk in enumerate(chunks):
            chunk_id = chunk["metadata"].get("id", str(uuid4()))
            chunk_content = chunk["metadata"].get("content", "")
            chunk_name = chunk["metadata"].get("name", f"chunk_{i+1}")
            content_preview = chunk_content[:200] + "..." if len(chunk_content) > 200 else chunk_content

            logger.debug(f"[CHUNK_INSERT] Preparing chunk {i+1}/{len(chunks)} - ID: {chunk_id}, Name: '{chunk_name}', Content: '{content_preview}'")

            points.append(
                PointStruct(
                    id=chunk_id,
                    vector=chunk["embeddings"],
                    payload=chunk["metadata"]
                )
            )

        # Insert points in batches to avoid memory issues
        batch_size = 100
        total_batches = (len(points) + batch_size - 1) // batch_size
        logger.debug(f"[CHUNK_INSERT] Inserting {len(points)} points in {total_batches} batch(es) of max {batch_size} points each")

        for batch_num, i in enumerate(range(0, len(points), batch_size), 1):
            batch = points[i:i + batch_size]
            logger.debug(f"[CHUNK_INSERT] Inserting batch {batch_num}/{total_batches} with {len(batch)} points")
            await client.upsert(
                collection_name=kb_id,
                points=batch
            )

        logger.info(f"[CHUNK_INSERT] Successfully inserted {len(chunks)} chunks into KB '{kb_name}'")

        # Log the IDs of all newly created chunks
        chunk_ids = [chunk["metadata"].get("id") for chunk in chunks]
        logger.debug(f"[CHUNK_INSERT] New chunk IDs created: {chunk_ids}")

    except Exception as e:
        logger.error(f"[CHUNK_INSERT] Error inserting chunks into KB '{kb_data.get('name', 'Unknown')}': {e}")
        raise


@logger.catch()
async def update_single_kb(kb_data: dict, file_path: str) -> dict:
    """Update a single knowledge base with the modified file"""
    kb_start_time = time.time()
    chunks_deleted = 0
    chunks_created = 0
    error = None
    status = "failed"
    kb_id = kb_data["id"]
    kb_name = kb_data.get("name", "Unknown")

    try:
        logger.info(f"[KB_UPDATE] Starting update of KB '{kb_name}' (ID: {kb_id}) for file '{file_path}'")

        # Step 1: Delete existing chunks
        logger.debug(f"[KB_UPDATE] Step 1/4: Deleting existing chunks for file '{file_path}' from KB '{kb_name}'")
        chunks_deleted = await delete_file_chunks_from_kb(kb_data, file_path)

        # Step 2: Process file into new chunks
        logger.debug(f"[KB_UPDATE] Step 2/4: Processing file '{file_path}' into chunks for KB '{kb_name}'")
        new_chunks = await process_file_chunks(file_path, kb_data)

        # Step 3: Generate embeddings
        logger.debug(f"[KB_UPDATE] Step 3/4: Generating embeddings for {len(new_chunks)} chunks in KB '{kb_name}'")
        await generate_embeddings_for_chunks(new_chunks)

        # Step 4: Insert new chunks
        logger.debug(f"[KB_UPDATE] Step 4/5: Inserting {len(new_chunks)} new chunks into KB '{kb_name}'")
        await insert_chunks_to_kb(kb_data, new_chunks)

        # Step 5: Update file timestamp in metadata
        logger.debug(f"[KB_UPDATE] Step 5/5: Updating file timestamp in metadata for KB '{kb_name}'")
        await update_kb_file_timestamp(kb_data, file_path, "update")

        chunks_created = len(new_chunks)
        status = "success"

        logger.info(f"[KB_UPDATE] Successfully updated KB '{kb_name}': deleted {chunks_deleted}, created {chunks_created} chunks in {time.time() - kb_start_time:.3f}s")

    except Exception as e:
        error = str(e)
        status = "failed"
        logger.error(f"[KB_UPDATE] Failed to update KB '{kb_name}': {e}")
        logger.debug(f"[KB_UPDATE] Full traceback: {traceback.format_exc()}")

    processing_time = time.time() - kb_start_time

    return {
        "kb_id": kb_id,
        "kb_name": kb_name,
        "status": status,
        "chunks_deleted": chunks_deleted,
        "chunks_created": chunks_created,
        "processing_time_seconds": processing_time,
        "error": error
    }


@logger.catch()
async def delete_single_kb_file(kb_data: dict, file_path: str) -> dict:
    """Delete all chunks for a specific file from a single knowledge base"""
    kb_start_time = time.time()
    chunks_deleted = 0
    error = None
    status = "failed"
    kb_id = kb_data["id"]
    kb_name = kb_data.get("name", "Unknown")

    try:
        logger.info(f"[KB_DELETE] Starting deletion of file '{file_path}' from KB '{kb_name}' (ID: {kb_id})")

        # Step 1: Delete existing chunks for the file
        logger.debug(f"[KB_DELETE] Step 1/2: Deleting all chunks for file '{file_path}' from KB '{kb_name}'")
        chunks_deleted = await delete_file_chunks_from_kb(kb_data, file_path)

        # Step 2: Remove file timestamp from metadata
        logger.debug(f"[KB_DELETE] Step 2/2: Removing file timestamp from metadata for KB '{kb_name}'")
        await update_kb_file_timestamp(kb_data, file_path, "delete")

        status = "success"
        logger.info(f"[KB_DELETE] Successfully deleted {chunks_deleted} chunks for file '{file_path}' from KB '{kb_name}' in {time.time() - kb_start_time:.3f}s")

    except Exception as e:
        error = str(e)
        status = "failed"
        logger.error(f"[KB_DELETE] Failed to delete file '{file_path}' from KB '{kb_name}': {e}")
        logger.debug(f"[KB_DELETE] Full traceback: {traceback.format_exc()}")

    processing_time = time.time() - kb_start_time

    return {
        "kb_id": kb_id,
        "kb_name": kb_name,
        "status": status,
        "chunks_deleted": chunks_deleted,
        "processing_time_seconds": processing_time,
        "error": error
    }


# Helper functions for finding knowledge bases containing files
@logger.catch()
async def find_kbs_containing_file(file_path: str) -> List[dict]:
    """Find all codebase knowledge bases that contain the specified file"""
    try:
        logger.debug(f"[KB_SEARCH] Searching for knowledge bases containing file: {file_path}")

        # Get all knowledge bases
        all_kbs = list_all_knowledge_bases()
        matching_kbs = []

        for kb in all_kbs:
            # Only check codebase type KBs
            kb_type = kb.get("metadata", {}).get("type", "")
            if kb_type != "Codebase":
                continue

            kb_id = kb["id"]
            kb_name = kb.get("name", "Unknown")

            try:
                # Check if this KB contains chunks for the file
                client = get_qdrant_client()
                file_filter = Filter(
                    must=[
                        FieldCondition(
                            key="file",
                            match=MatchValue(value=file_path)
                        )
                    ]
                )

                # Search for chunks with this file path
                result = await client.scroll(
                    collection_name=kb_id,
                    scroll_filter=file_filter,
                    limit=1,  # We only need to know if any exist
                    with_payload=False,
                    with_vectors=False
                )

                if result[0]:  # If any chunks found
                    matching_kbs.append(kb)
                    logger.debug(f"[KB_SEARCH] KB '{kb_name}' contains file '{file_path}'")
                else:
                    logger.debug(f"[KB_SEARCH] KB '{kb_name}' does not contain file '{file_path}'")

            except Exception as e:
                logger.warning(f"[KB_SEARCH] Error checking KB '{kb_name}' for file '{file_path}': {e}")
                continue

        logger.info(f"[KB_SEARCH] Found {len(matching_kbs)} knowledge bases containing file '{file_path}'")
        return matching_kbs

    except Exception as e:
        logger.error(f"[KB_SEARCH] Error searching for knowledge bases containing file '{file_path}': {e}")
        return []


# -----------------------------------------------------------------------------
# Main Route Handler
# -----------------------------------------------------------------------------

@logger.catch()
async def watchdog_file_update(file_path: str) -> dict:
    """Handle file update notification from watchdog server"""
    start_time = time.time()
    timestamp = datetime.now(timezone.utc).isoformat()

    logger.info(f"[ENDPOINT] ========== FILE UPDATE ENDPOINT CALLED ==========")
    logger.info(f"[ENDPOINT] Processing file update request for: {file_path}")
    logger.info(f"[ENDPOINT] Request timestamp: {timestamp}")

    try:
        # Step 1: Validate file path
        logger.debug(f"[ENDPOINT] Step 1: Validating file path '{file_path}'")
        file_path_str = validate_file_path(file_path)
        logger.debug(f"[ENDPOINT] File path validation successful: '{file_path_str}'")

        # Step 2: Discovery phase - find all KBs containing this file
        logger.info(f"[ENDPOINT] Step 2: Discovering knowledge bases containing file: {file_path_str}")
        matching_kbs = await find_kbs_containing_file(file_path_str)

        if not matching_kbs:
            logger.warning(f"[ENDPOINT] File '{file_path_str}' not found in any knowledge base")
            return {
                "status": "success",
                "file_path": file_path_str,
                "timestamp": timestamp,
                "processing_time_seconds": time.time() - start_time,
                "summary": {
                    "total_kbs_found": 0,
                    "kbs_updated_successfully": 0,
                    "kbs_failed": 0,
                    "total_chunks_deleted": 0,
                    "total_chunks_created": 0,
                    "total_embeddings_generated": 0
                },
                "kb_results": [],
                "warnings": ["File not found in any knowledge base"],
                "next_actions": ["Verify file path and knowledge base content"]
            }

        logger.info(f"[ENDPOINT] Found file in {len(matching_kbs)} knowledge base(s)")

        # Step 3: Update each knowledge base
        logger.info(f"[ENDPOINT] Step 3: Starting knowledge base updates")
        kb_results = []
        successful_updates = 0
        failed_updates = 0
        total_chunks_deleted = 0
        total_chunks_created = 0

        # Process each KB
        logger.info(f"[ENDPOINT] Processing {len(matching_kbs)} knowledge base(s) for updates")
        for i, kb_data in enumerate(matching_kbs, 1):
            kb_name = kb_data.get("name", "Unknown")
            kb_id = kb_data["id"]
            logger.info(f"[ENDPOINT] Processing KB {i}/{len(matching_kbs)}: '{kb_name}' (ID: {kb_id})")

            result = await update_single_kb(kb_data, file_path_str)
            kb_results.append(result)

            if result["status"] == "success":
                successful_updates += 1
                total_chunks_deleted += result["chunks_deleted"]
                total_chunks_created += result["chunks_created"]
                logger.info(f"[ENDPOINT] KB {i}/{len(matching_kbs)} update successful: '{kb_name}'")
            else:
                failed_updates += 1
                logger.error(f"[ENDPOINT] KB {i}/{len(matching_kbs)} update failed: '{kb_name}' - {result.get('error')}")

        # Step 4: Generate response
        total_processing_time = time.time() - start_time

        # Determine overall status
        if failed_updates == 0:
            overall_status = "success"
        elif successful_updates > 0:
            overall_status = "partial"
        else:
            overall_status = "failed"

        # Generate warnings and next actions
        warnings = []
        next_actions = []

        if failed_updates > 0:
            warnings.append(f"{failed_updates} knowledge base(s) failed to update")
            next_actions.append("Review failed KB updates in the logs")

        if total_chunks_created > total_chunks_deleted * 1.5:
            warnings.append("File size increased significantly - consider reviewing chunking parameters")

        if overall_status == "partial":
            next_actions.append("Consider manual verification of updated content")



        response = {
            "status": overall_status,
            "file_path": file_path_str,
            "timestamp": timestamp,
            "processing_time_seconds": total_processing_time,
            "summary": {
                "total_kbs_found": len(matching_kbs),
                "kbs_updated_successfully": successful_updates,
                "kbs_failed": failed_updates,
                "total_chunks_deleted": total_chunks_deleted,
                "total_chunks_created": total_chunks_created,
                "total_embeddings_generated": total_chunks_created
            },
            "kb_results": kb_results,
            "warnings": warnings,
            "next_actions": next_actions
        }

        logger.info(
            f"[ENDPOINT] ========== FILE UPDATE COMPLETED ==========\n"
            f"[ENDPOINT] Status: {overall_status}\n"
            f"[ENDPOINT] File: {file_path_str}\n"
            f"[ENDPOINT] KBs Updated: {successful_updates}/{len(matching_kbs)}\n"
            f"[ENDPOINT] Total Chunks Deleted: {total_chunks_deleted}\n"
            f"[ENDPOINT] Total Chunks Created: {total_chunks_created}\n"
            f"[ENDPOINT] Processing Time: {total_processing_time:.3f}s\n"
            f"[ENDPOINT] ================================================"
        )

        return response

    except Exception as e:
        total_processing_time = time.time() - start_time
        logger.error(f"Unexpected error in file update: {e}")
        logger.debug(f"Full traceback: {traceback.format_exc()}")

        # Log failed operation


        return {
            "status": "error",
            "file_path": file_path,
            "timestamp": timestamp,
            "processing_time_seconds": total_processing_time,
            "error": str(e)
        }


@logger.catch()
async def watchdog_file_delete(file_path: str) -> dict:
    """Handle file deletion notification from watchdog server"""
    start_time = time.time()
    timestamp = datetime.now(timezone.utc).isoformat()

    logger.info(f"[ENDPOINT] ========== FILE DELETE ENDPOINT CALLED ==========")
    logger.info(f"[ENDPOINT] Processing file deletion request for: {file_path}")
    logger.info(f"[ENDPOINT] Request timestamp: {timestamp}")

    try:
        # Step 1: Basic validation (don't require file to exist since it's deleted)
        logger.debug(f"[ENDPOINT] Step 1: Validating file path '{file_path}'")
        if ".." in file_path:
            raise ValueError("Invalid file path: directory traversal detected")

        file_path_str = file_path
        logger.debug(f"[ENDPOINT] File path validation successful: '{file_path_str}'")

        # Step 2: Discovery phase - find all KBs containing this file
        logger.info(f"[ENDPOINT] Step 2: Discovering knowledge bases containing file: {file_path_str}")
        matching_kbs = await find_kbs_containing_file(file_path_str)

        if not matching_kbs:
            logger.warning(f"[ENDPOINT] File '{file_path_str}' not found in any knowledge base")
            return {
                "status": "success",
                "file_path": file_path_str,
                "timestamp": timestamp,
                "processing_time_seconds": time.time() - start_time,
                "summary": {
                    "total_kbs_found": 0,
                    "kbs_processed_successfully": 0,
                    "kbs_failed": 0,
                    "total_chunks_deleted": 0
                },
                "kb_results": [],
                "warnings": ["File not found in any knowledge base"],
                "next_actions": ["Verify file path and knowledge base content"]
            }

        logger.info(f"[ENDPOINT] Found file in {len(matching_kbs)} knowledge base(s)")

        # Step 3: Delete from each knowledge base
        logger.info(f"[ENDPOINT] Step 3: Starting knowledge base deletions")
        kb_results = []
        successful_deletions = 0
        failed_deletions = 0
        total_chunks_deleted = 0

        # Process each KB
        logger.info(f"[ENDPOINT] Processing {len(matching_kbs)} knowledge base(s) for deletions")
        for i, kb_data in enumerate(matching_kbs, 1):
            kb_name = kb_data.get("name", "Unknown")
            kb_id = kb_data["id"]
            logger.info(f"[ENDPOINT] Processing KB {i}/{len(matching_kbs)}: '{kb_name}' (ID: {kb_id})")

            result = await delete_single_kb_file(kb_data, file_path_str)
            kb_results.append(result)

            if result["status"] == "success":
                successful_deletions += 1
                total_chunks_deleted += result["chunks_deleted"]
                logger.info(f"[ENDPOINT] KB {i}/{len(matching_kbs)} deletion successful: '{kb_name}'")
            else:
                failed_deletions += 1
                logger.error(f"[ENDPOINT] KB {i}/{len(matching_kbs)} deletion failed: '{kb_name}' - {result.get('error')}")

        # Step 4: Generate response
        total_processing_time = time.time() - start_time

        # Determine overall status
        if failed_deletions == 0:
            overall_status = "success"
        elif successful_deletions > 0:
            overall_status = "partial"
        else:
            overall_status = "failed"

        # Generate warnings and next actions
        warnings = []
        next_actions = []

        if failed_deletions > 0:
            warnings.append(f"{failed_deletions} knowledge base(s) failed to process deletion")
            next_actions.append("Review failed KB deletions in the logs")

        if total_chunks_deleted == 0 and matching_kbs:
            warnings.append("No chunks were deleted despite file being found in knowledge bases")
            next_actions.append("Verify chunk deletion logic and file path matching")

        if overall_status == "partial":
            next_actions.append("Consider manual verification of remaining content")



        response = {
            "status": overall_status,
            "file_path": file_path_str,
            "timestamp": timestamp,
            "processing_time_seconds": total_processing_time,
            "summary": {
                "total_kbs_found": len(matching_kbs),
                "kbs_processed_successfully": successful_deletions,
                "kbs_failed": failed_deletions,
                "total_chunks_deleted": total_chunks_deleted
            },
            "kb_results": kb_results,
            "warnings": warnings,
            "next_actions": next_actions
        }

        logger.info(
            f"[ENDPOINT] ========== FILE DELETE COMPLETED ==========\n"
            f"[ENDPOINT] Status: {overall_status}\n"
            f"[ENDPOINT] File: {file_path_str}\n"
            f"[ENDPOINT] KBs Processed: {successful_deletions}/{len(matching_kbs)}\n"
            f"[ENDPOINT] Total Chunks Deleted: {total_chunks_deleted}\n"
            f"[ENDPOINT] Processing Time: {total_processing_time:.3f}s\n"
            f"[ENDPOINT] ================================================"
        )

        return response

    except Exception as e:
        total_processing_time = time.time() - start_time
        logger.error(f"Unexpected error in file deletion: {e}")
        logger.debug(f"Full traceback: {traceback.format_exc()}")

        # Log failed operation

        return {
            "status": "error",
            "file_path": file_path,
            "timestamp": timestamp,
            "processing_time_seconds": total_processing_time,
            "error": str(e)
        }
