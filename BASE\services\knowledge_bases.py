from pathlib import Path
from database.MicroMongo import MongoClient
from BASE.vdb.qdrant import get_qdrant_client
from qdrant_client.models import Distance, VectorParams
from logger.log import logger

# Initialize MicroMongo client and database
_client = None
_db = None

def get_knowledge_bases_collection():
    """Get the knowledge_bases collection from MicroMongo database."""
    global _client, _db

    if _client is None:
        _client = MongoClient('knowledge_bases_db')
        _db = _client['codemate']

    return _db['knowledge_bases']

def create_knowledge_base(name: str, kbid: str, metadata: dict = None, is_auto_indexed: bool = False):
    """Create a new knowledge base entry."""
    collection = get_knowledge_bases_collection()

    kb_data = {
        "name": name,
        "id": kbid,
        "metadata": metadata or {},
        "isAutoIndexed": is_auto_indexed
    }

    result = collection.insert_one(kb_data)
    return result.inserted_id

def get_knowledge_base_by_name(name: str):
    """Get a knowledge base by name."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"name": name})

def get_knowledge_base_by_id(kbid: str):
    """Get a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.find_one({"id": kbid})

def get_knowledge_base_by_path(path: str):
    """Get a knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({"metadata.path": normalized_path})

def get_auto_indexed_knowledge_base_by_path(path: str):
    """Get an auto-indexed knowledge base by path."""
    collection = get_knowledge_bases_collection()
    normalized_path = str(Path(path).resolve())
    return collection.find_one({
        "metadata.path": normalized_path,
        "isAutoIndexed": True
    })

def exists_knowledge_base_by_name(name: str) -> bool:
    """Check if a knowledge base with the given name exists."""
    return get_knowledge_base_by_name(name) is not None

def exists_knowledge_base_by_id(kbid: str) -> bool:
    """Check if a knowledge base with the given ID exists."""
    return get_knowledge_base_by_id(kbid) is not None

def exists_knowledge_base_by_path(path: str) -> bool:
    """Check if a knowledge base with the given path exists."""
    return get_knowledge_base_by_path(path) is not None

def exists_auto_indexed_knowledge_base_by_path(path: str) -> bool:
    """Check if an auto-indexed knowledge base with the given path exists."""
    return get_auto_indexed_knowledge_base_by_path(path) is not None

def update_knowledge_base(kbid: str, update_data: dict):
    """Update a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": update_data})

def update_knowledge_base_cloud_id(kbid: str, cloud_id: str = None):
    """Update the cloud_id field of a knowledge base."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": {"metadata.cloud_id": cloud_id}})

def update_knowledge_base_sync_timestamp(kbid: str, timestamp: int):
    """Update the lastSynced timestamp of a knowledge base."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": {"metadata.syncConfig.lastSynced": timestamp}})

def update_knowledge_base_sync_config(kbid: str, sync_config: dict):
    """Update the sync configuration of a knowledge base."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": {"metadata.syncConfig": sync_config}})

def enable_knowledge_base_sync(kbid: str):
    """Enable cloud sync for a knowledge base."""
    collection = get_knowledge_bases_collection()
    return collection.update_one({"id": kbid}, {"$set": {"metadata.syncConfig.enabled": True}})

@logger.catch()
async def get_all_chunks_from_kb(kbid: str) -> list[dict]:
    """
    Get all chunks from a knowledge base using Qdrant.

    Args:
        kbid: Knowledge base ID

    Returns:
        List of chunk dictionaries
    """
    try:
        qdrant_client = get_qdrant_client()

        # Get all points from the collection
        result = await qdrant_client.scroll(
            collection_name=kbid,
            limit=10000,  # Adjust as needed
            with_payload=True,
            with_vectors=True
        )

        chunks = []
        for point in result[0]:  # result is (points, next_page_offset)
            chunk = {
                "metadata": point.payload,
                "embeddings": point.vector
            }
            chunks.append(chunk)

        logger.info(f"Retrieved {len(chunks)} chunks from knowledge base {kbid}")
        return chunks

    except Exception as e:
        logger.error(f"Error retrieving chunks from knowledge base {kbid}: {e}")
        return []

def delete_knowledge_base(kbid: str):
    """Delete a knowledge base by ID."""
    collection = get_knowledge_bases_collection()
    return collection.delete_one({"id": kbid})

def list_all_knowledge_bases():
    """Get all knowledge bases."""
    collection = get_knowledge_bases_collection()
    return list(collection.find())

def count_knowledge_bases():
    """Count total number of knowledge bases."""
    collection = get_knowledge_bases_collection()
    return collection.count_documents({})

@logger.catch()
async def from_chunks(metadata: dict, chunks: list[dict]) -> dict:
    """
    Save processed chunks to Qdrant database using functional programming approach.

    Args:
        metadata: Dictionary containing knowledge base metadata with structure:
            {
                "id": str,
                "cloud_id": None,
                "name": str,
                "description": str,
                "type": str,
                "source": str,
                "scope": str,
                "syncConfig": {"enabled": bool, "lastSynced": int},
                "isAutoIndexed": bool,
                "metadata": {...}  # Type-specific metadata
            }
        chunks: List of chunk dictionaries with structure:
            {
                "metadata": {
                    "id": str,
                    "file": str,
                    "name": str,
                    "content": str,
                    "additional_metadata": {...}
                },
                "embeddings": [float, ...]
            }

    Returns:
        Dictionary representing the created knowledge base
    """
    if not chunks:
        raise ValueError("No chunks provided")

    kb_id = metadata["id"]
    vector_size = len(chunks[0]["embeddings"])

    logger.info(f"Creating Qdrant collection for knowledge base: {kb_id}")

    # Get Qdrant client and create collection
    qdrant_client = get_qdrant_client()
    collection_name = kb_id

    await qdrant_client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
    )

    logger.info(f"Qdrant collection '{collection_name}' created successfully")

    # Store chunks with embeddings
    points = []
    for i, chunk in enumerate(chunks):
        points.append({
            "id": i,
            "vector": chunk["embeddings"],
            "payload": chunk["metadata"]
        })

    if points:
        logger.info(f"Storing {len(points)} points in Qdrant collection")
        await qdrant_client.upsert(collection_name=collection_name, points=points)
        logger.info("All chunks stored in Qdrant successfully")
    else:
        logger.warning("No points to store in Qdrant")

    # Create knowledge base entry in database
    kb_data = {
        "name": metadata["name"],
        "id": kb_id,
        "metadata": metadata,
        "isAutoIndexed": metadata.get("isAutoIndexed", False),
    }

    collection = get_knowledge_bases_collection()
    result = collection.insert_one(kb_data)
    logger.info(f"Knowledge base entry created in database with ID: {result.inserted_id}")

    return kb_data


def find_file_across_all_kbs(
        file_path: str,
        sort_by_line: bool = True,
        include_kb_metadata: bool = True,
        max_retries_per_kb: int = 3
    ) -> QdrantCrossKBSearchResult:
        """
        Search for a file path across all knowledge bases in the system.

        Args:
            file_path: The file path to search for in chunk metadata
            sort_by_line: Whether to sort chunks by line number within each KB
            include_kb_metadata: Whether to include detailed KB metadata in results
            max_retries_per_kb: Maximum number of retries for each KB if search fails

        Returns:
            QdrantCrossKBSearchResult containing comprehensive search results

        Raises:
            Exception: If no knowledge bases exist in the system
        """
        import time
        from client_server.core.logger import LOGGER

        search_start_time = time.time()

        # Get all knowledge bases
        try:
            all_kbs = QdrantKnowledgeBase.get_all()
        except Exception as e:
            LOGGER.error(f"Failed to retrieve knowledge bases: {e}")
            raise Exception(f"Could not access knowledge base system: {e}")

        if not all_kbs:
            raise Exception("No knowledge bases found in the system")

        LOGGER.info(f"Starting cross-KB search for file '{file_path}' across {len(all_kbs)} knowledge bases")

        kb_results = []
        total_chunks_found = 0
        total_content_length = 0
        kbs_with_matches = 0
        search_errors = []

        # Search each knowledge base
        for kb_index, kb in enumerate(all_kbs):
            kb_search_start = time.time()
            retries_left = max_retries_per_kb
            kb_error = None
            chunks = []

            LOGGER.debug(f"Searching KB {kb_index + 1}/{len(all_kbs)}: {kb.name} (ID: {kb.id})")

            # Retry logic for each KB
            while retries_left > 0:
                try:
                    chunks = kb.get_chunks_by_file_path(file_path, sort_by_line=sort_by_line)
                    kb_error = None
                    break
                except Exception as e:
                    retries_left -= 1
                    kb_error = str(e)
                    LOGGER.warning(f"Error searching KB {kb.name} (attempt {max_retries_per_kb - retries_left}): {e}")
                    if retries_left > 0:
                        time.sleep(0.1)  # Brief delay before retry

            kb_search_time = time.time() - kb_search_start

            # Calculate KB-specific metrics
            kb_total_content = sum(len(chunk.metadata.content) for chunk in chunks) if chunks else 0
            chunk_ids = [chunk.metadata.id for chunk in chunks] if chunks else []

            # Create KB result
            kb_result = QdrantKnowledgeBaseSearchResult(
                kb_id=kb.id,
                kb_name=kb.name,
                kb_description=kb.description if include_kb_metadata else "",
                kb_type=str(kb.type) if include_kb_metadata else "",
                file_path=file_path,
                chunks=chunks,
                chunk_ids=chunk_ids,
                total_chunks=len(chunks),
                total_content_length=kb_total_content,
                search_time_seconds=kb_search_time,
                error=kb_error
            )

            kb_results.append(kb_result)

            # Update overall statistics
            if chunks:
                kbs_with_matches += 1
                total_chunks_found += len(chunks)
                total_content_length += kb_total_content
                LOGGER.debug(f"Found {len(chunks)} chunks in KB {kb.name}")
            else:
                LOGGER.debug(f"No chunks found in KB {kb.name}")

            # Track errors
            if kb_error:
                error_msg = f"KB '{kb.name}' (ID: {kb.id}): {kb_error}"
                search_errors.append(error_msg)

        total_search_time = time.time() - search_start_time

        # Create comprehensive result
        result = QdrantCrossKBSearchResult(
            file_path=file_path,
            total_kbs_searched=len(all_kbs),
            kbs_with_matches=kbs_with_matches,
            total_chunks_found=total_chunks_found,
            total_content_length=total_content_length,
            search_time_seconds=total_search_time,
            kb_results=kb_results,
            errors=search_errors
        )

        LOGGER.info(
            f"Cross-KB search completed: found file in {kbs_with_matches}/{len(all_kbs)} KBs, "
            f"{total_chunks_found} total chunks, {total_search_time:.3f}s"
        )

        return result
    

